{"name": "app-recommendation-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "node scripts/setup-database.js setup", "db:setup-with-seed": "node scripts/setup-database.js setup --seed", "db:reset": "node scripts/setup-database.js reset", "db:seed": "node scripts/setup-database.js seed"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "lucide-react": "^0.515.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.0", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}