import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { appSchema, appSearchSchema } from '@/lib/validations/schemas'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 验证查询参数
    const queryValidation = appSearchSchema.safeParse({
      query: searchParams.get('query') || undefined,
      category: searchParams.get('category') || undefined,
      sort_by: searchParams.get('sort_by') || 'hot_score',
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20')
    })
    
    if (!queryValidation.success) {
      return NextResponse.json(
        { error: '查询参数无效', details: queryValidation.error.errors },
        { status: 400 }
      )
    }
    
    const { query, category, sort_by, page, limit } = queryValidation.data
    const offset = (page - 1) * limit
    
    const supabase = await createClient()
    
    // 构建查询
    let dbQuery = supabase
      .from('apps')
      .select(`
        *,
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        )
      `)
    
    // 添加搜索条件
    if (query) {
      dbQuery = dbQuery.or(`name.ilike.%${query}%,description.ilike.%${query}%,developer.ilike.%${query}%`)
    }
    
    if (category) {
      dbQuery = dbQuery.eq('category', category)
    }
    
    // 排序
    const sortColumn = sort_by === 'hot_score' ? 'hot_score' : 
                      sort_by === 'activity_count' ? 'activity_count' : 'created_at'
    dbQuery = dbQuery.order(sortColumn, { ascending: false })
    
    // 分页
    dbQuery = dbQuery.range(offset, offset + limit - 1)
    
    const { data: apps, error } = await dbQuery
    
    if (error) {
      console.error('获取应用列表失败:', error)
      return NextResponse.json(
        { error: '获取应用列表失败' },
        { status: 500 }
      )
    }
    
    // 获取总数（用于分页）
    let countQuery = supabase
      .from('apps')
      .select('*', { count: 'exact', head: true })
    
    if (query) {
      countQuery = countQuery.or(`name.ilike.%${query}%,description.ilike.%${query}%,developer.ilike.%${query}%`)
    }
    
    if (category) {
      countQuery = countQuery.eq('category', category)
    }
    
    const { count, error: countError } = await countQuery
    
    if (countError) {
      console.error('获取应用总数失败:', countError)
    }
    
    return NextResponse.json({
      apps,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    })
    
  } catch (error) {
    console.error('获取应用列表错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = appSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const appData = {
      ...validation.data,
      created_by: user.id
    }
    
    // 创建应用
    const { data: app, error: createError } = await supabase
      .from('apps')
      .insert(appData)
      .select(`
        *,
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        )
      `)
      .single()
    
    if (createError) {
      console.error('创建应用失败:', createError)
      return NextResponse.json(
        { error: '创建应用失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: '应用创建成功',
      app
    }, { status: 201 })
    
  } catch (error) {
    console.error('创建应用错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
