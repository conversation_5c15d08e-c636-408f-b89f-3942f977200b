import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { appUpdateSchema } from '@/lib/validations/schemas'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: app, error } = await supabase
      .from('apps')
      .select(`
        *,
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        ),
        activities (
          id,
          title,
          description,
          total_codes,
          used_codes,
          is_active,
          expires_at,
          created_at,
          requires_verification,
          requires_subscription,
          feedback_required
        )
      `)
      .eq('id', params.id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: '应用不存在' },
          { status: 404 }
        )
      }
      
      console.error('获取应用详情失败:', error)
      return NextResponse.json(
        { error: '获取应用详情失败' },
        { status: 500 }
      )
    }
    
    // 计算活动统计
    const activeActivities = app.activities?.filter(activity => 
      activity.is_active && 
      (!activity.expires_at || new Date(activity.expires_at) > new Date())
    ) || []
    
    const totalCodes = app.activities?.reduce((sum, activity) => sum + activity.total_codes, 0) || 0
    const usedCodes = app.activities?.reduce((sum, activity) => sum + activity.used_codes, 0) || 0
    
    const appWithStats = {
      ...app,
      stats: {
        total_activities: app.activities?.length || 0,
        active_activities: activeActivities.length,
        total_codes: totalCodes,
        used_codes: usedCodes,
        claim_rate: totalCodes > 0 ? (usedCodes / totalCodes) * 100 : 0
      }
    }
    
    return NextResponse.json({ app: appWithStats })
    
  } catch (error) {
    console.error('获取应用详情错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 检查应用是否存在且用户有权限修改
    const { data: existingApp, error: fetchError } = await supabase
      .from('apps')
      .select('created_by')
      .eq('id', params.id)
      .single()
    
    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: '应用不存在' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: '获取应用信息失败' },
        { status: 500 }
      )
    }
    
    if (existingApp.created_by !== user.id) {
      return NextResponse.json(
        { error: '无权限修改此应用' },
        { status: 403 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = appUpdateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const updates = validation.data
    
    // 更新应用
    const { data: app, error: updateError } = await supabase
      .from('apps')
      .update(updates)
      .eq('id', params.id)
      .select(`
        *,
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        )
      `)
      .single()
    
    if (updateError) {
      console.error('更新应用失败:', updateError)
      return NextResponse.json(
        { error: '更新应用失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: '应用更新成功',
      app
    })
    
  } catch (error) {
    console.error('更新应用错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 检查应用是否存在且用户有权限删除
    const { data: existingApp, error: fetchError } = await supabase
      .from('apps')
      .select('created_by, name')
      .eq('id', params.id)
      .single()
    
    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: '应用不存在' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: '获取应用信息失败' },
        { status: 500 }
      )
    }
    
    if (existingApp.created_by !== user.id) {
      return NextResponse.json(
        { error: '无权限删除此应用' },
        { status: 403 }
      )
    }
    
    // 检查是否有活跃的活动
    const { data: activeActivities, error: activitiesError } = await supabase
      .from('activities')
      .select('id')
      .eq('app_id', params.id)
      .eq('is_active', true)
    
    if (activitiesError) {
      console.error('检查活动状态失败:', activitiesError)
      return NextResponse.json(
        { error: '检查活动状态失败' },
        { status: 500 }
      )
    }
    
    if (activeActivities && activeActivities.length > 0) {
      return NextResponse.json(
        { error: '无法删除有活跃活动的应用，请先停止所有活动' },
        { status: 409 }
      )
    }
    
    // 删除应用（级联删除会自动处理相关数据）
    const { error: deleteError } = await supabase
      .from('apps')
      .delete()
      .eq('id', params.id)
    
    if (deleteError) {
      console.error('删除应用失败:', deleteError)
      return NextResponse.json(
        { error: '删除应用失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: `应用 "${existingApp.name}" 删除成功`
    })
    
  } catch (error) {
    console.error('删除应用错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
