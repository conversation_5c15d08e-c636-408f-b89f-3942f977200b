import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { activitySchema, activitySearchSchema } from '@/lib/validations/schemas'
import { validateActivityConditions } from '@/lib/validations/schemas'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 验证查询参数
    const queryValidation = activitySearchSchema.safeParse({
      app_id: searchParams.get('app_id') || undefined,
      is_active: searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20')
    })
    
    if (!queryValidation.success) {
      return NextResponse.json(
        { error: '查询参数无效', details: queryValidation.error.errors },
        { status: 400 }
      )
    }
    
    const { app_id, is_active, page, limit } = queryValidation.data
    const offset = (page - 1) * limit
    
    const supabase = await createClient()
    
    // 构建查询
    let dbQuery = supabase
      .from('activities')
      .select(`
        *,
        apps (
          id,
          name,
          icon_url,
          category,
          developer
        ),
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        )
      `)
    
    // 添加筛选条件
    if (app_id) {
      dbQuery = dbQuery.eq('app_id', app_id)
    }
    
    if (is_active !== undefined) {
      dbQuery = dbQuery.eq('is_active', is_active)
    }
    
    // 排序（活跃的活动优先，然后按创建时间）
    dbQuery = dbQuery.order('is_active', { ascending: false })
    dbQuery = dbQuery.order('created_at', { ascending: false })
    
    // 分页
    dbQuery = dbQuery.range(offset, offset + limit - 1)
    
    const { data: activities, error } = await dbQuery
    
    if (error) {
      console.error('获取活动列表失败:', error)
      return NextResponse.json(
        { error: '获取活动列表失败' },
        { status: 500 }
      )
    }
    
    // 获取总数（用于分页）
    let countQuery = supabase
      .from('activities')
      .select('*', { count: 'exact', head: true })
    
    if (app_id) {
      countQuery = countQuery.eq('app_id', app_id)
    }
    
    if (is_active !== undefined) {
      countQuery = countQuery.eq('is_active', is_active)
    }
    
    const { count, error: countError } = await countQuery
    
    if (countError) {
      console.error('获取活动总数失败:', countError)
    }
    
    return NextResponse.json({
      activities,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    })
    
  } catch (error) {
    console.error('获取活动列表错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 获取用户资料
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('is_verified, is_vip, vip_expires_at')
      .eq('id', user.id)
      .single()
    
    if (profileError) {
      return NextResponse.json(
        { error: '获取用户资料失败' },
        { status: 500 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = activitySchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const activityData = validation.data
    
    // 检查用户是否有权限设置高级条件
    const isVip = userProfile.is_vip && 
      (!userProfile.vip_expires_at || new Date(userProfile.vip_expires_at) > new Date())
    
    const conditionValidation = validateActivityConditions(
      isVip,
      activityData.requires_verification,
      activityData.requires_subscription,
      activityData.required_apps,
      activityData.feedback_required,
      activityData.feedback_questions
    )
    
    if (!conditionValidation.isValid) {
      return NextResponse.json(
        { error: '活动条件设置无效', details: conditionValidation.errors },
        { status: 400 }
      )
    }
    
    // 验证应用是否存在且用户有权限
    const { data: app, error: appError } = await supabase
      .from('apps')
      .select('created_by')
      .eq('id', activityData.app_id)
      .single()
    
    if (appError) {
      return NextResponse.json(
        { error: '应用不存在' },
        { status: 404 }
      )
    }
    
    if (app.created_by !== user.id) {
      return NextResponse.json(
        { error: '只能为自己的应用创建活动' },
        { status: 403 }
      )
    }
    
    // 创建活动
    const { data: activity, error: createError } = await supabase
      .from('activities')
      .insert({
        ...activityData,
        total_codes: activityData.invite_codes.length,
        created_by: user.id
      })
      .select(`
        *,
        apps (
          id,
          name,
          icon_url,
          category,
          developer
        ),
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        )
      `)
      .single()
    
    if (createError) {
      console.error('创建活动失败:', createError)
      return NextResponse.json(
        { error: '创建活动失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: '活动创建成功',
      activity
    }, { status: 201 })
    
  } catch (error) {
    console.error('创建活动错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
