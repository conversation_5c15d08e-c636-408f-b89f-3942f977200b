import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

const vipUpgradeSchema = z.object({
  plan: z.enum(['monthly', 'yearly']),
  payment_method: z.string().optional()
})

// VIP计划配置
const VIP_PLANS = {
  monthly: {
    name: '月度VIP',
    price: 29.9,
    duration_months: 1,
    features: [
      '设置实名认证要求',
      '设置应用订阅要求',
      '添加反馈问卷',
      '高级活动统计',
      '批量创建活动',
      '优先展示标识'
    ]
  },
  yearly: {
    name: '年度VIP',
    price: 299.9,
    duration_months: 12,
    features: [
      '设置实名认证要求',
      '设置应用订阅要求',
      '添加反馈问卷',
      '高级活动统计',
      '批量创建活动',
      '优先展示标识',
      '专属客服支持',
      '数据导出功能'
    ]
  }
} as const

// 模拟支付服务
async function mockPaymentService(userId: string, plan: keyof typeof VIP_PLANS) {
  // 在实际项目中，这里会调用真实的支付API
  // 例如：支付宝、微信支付、Stripe等
  
  await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟支付处理延迟
  
  // 模拟支付成功
  return {
    success: true,
    payment_id: `pay_${Date.now()}`,
    amount: VIP_PLANS[plan].price,
    plan
  }
}

export async function GET() {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const { data: profile } = await supabase
      .from('users')
      .select('is_vip, vip_expires_at')
      .eq('id', user.id)
      .single()
    
    const isVip = profile?.is_vip && 
      (!profile.vip_expires_at || new Date(profile.vip_expires_at) > new Date())
    
    return NextResponse.json({
      is_vip: isVip,
      vip_expires_at: profile?.vip_expires_at,
      plans: VIP_PLANS
    })
    
  } catch (error) {
    console.error('获取VIP状态错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = vipUpgradeSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const { plan } = validation.data
    
    // 检查计划是否存在
    if (!VIP_PLANS[plan]) {
      return NextResponse.json(
        { error: '无效的VIP计划' },
        { status: 400 }
      )
    }
    
    // 检查用户当前VIP状态
    const { data: profile } = await supabase
      .from('users')
      .select('is_vip, vip_expires_at')
      .eq('id', user.id)
      .single()
    
    const currentVipExpiry = profile?.vip_expires_at ? new Date(profile.vip_expires_at) : null
    const isCurrentlyVip = profile?.is_vip && currentVipExpiry && currentVipExpiry > new Date()
    
    try {
      // 调用支付服务
      const paymentResult = await mockPaymentService(user.id, plan)
      
      if (paymentResult.success) {
        // 计算新的过期时间
        const now = new Date()
        const startDate = isCurrentlyVip && currentVipExpiry ? currentVipExpiry : now
        const expiryDate = new Date(startDate)
        expiryDate.setMonth(expiryDate.getMonth() + VIP_PLANS[plan].duration_months)
        
        // 更新用户VIP状态
        const { error: updateError } = await supabase
          .from('users')
          .update({ 
            is_vip: true,
            vip_expires_at: expiryDate.toISOString()
          })
          .eq('id', user.id)
        
        if (updateError) {
          console.error('更新VIP状态失败:', updateError)
          return NextResponse.json(
            { error: '更新VIP状态失败' },
            { status: 500 }
          )
        }
        
        return NextResponse.json({
          message: 'VIP升级成功',
          payment_id: paymentResult.payment_id,
          plan: VIP_PLANS[plan],
          expires_at: expiryDate.toISOString()
        })
      } else {
        return NextResponse.json(
          { error: '支付失败，请重试' },
          { status: 400 }
        )
      }
      
    } catch (paymentError) {
      console.error('支付服务错误:', paymentError)
      return NextResponse.json(
        { error: '支付服务异常，请稍后重试' },
        { status: 500 }
      )
    }
    
  } catch (error) {
    console.error('VIP升级错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

// 取消VIP（仅用于测试）
export async function DELETE() {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 仅在开发环境允许取消VIP
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: '此操作仅在开发环境可用' },
        { status: 403 }
      )
    }
    
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        is_vip: false,
        vip_expires_at: null
      })
      .eq('id', user.id)
    
    if (updateError) {
      return NextResponse.json(
        { error: '取消VIP失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: 'VIP已取消'
    })
    
  } catch (error) {
    console.error('取消VIP错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
