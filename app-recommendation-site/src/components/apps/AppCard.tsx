'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Database } from '@/types/database'
import { getCategoryInfo, formatTimeAgo } from '@/lib/utils/business'
import { StarIcon, FireIcon, ClockIcon, UserIcon } from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

type App = Database['public']['Tables']['apps']['Row'] & {
  users: {
    username: string | null
    full_name: string | null
    is_verified: boolean
    is_vip: boolean
  } | null
}

interface AppCardProps {
  app: App
  showStats?: boolean
  className?: string
}

export default function AppCard({ app, showStats = true, className = '' }: AppCardProps) {
  const categoryInfo = getCategoryInfo(app.category)
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 ${className}`}>
      <div className="p-6">
        {/* 应用头部信息 */}
        <div className="flex items-start space-x-4">
          {/* 应用图标 */}
          <div className="flex-shrink-0">
            {app.icon_url ? (
              <Image
                src={app.icon_url}
                alt={app.name}
                width={64}
                height={64}
                className="rounded-lg"
                onError={(e) => {
                  // 图标加载失败时显示默认图标
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  target.nextElementSibling?.classList.remove('hidden')
                }}
              />
            ) : null}
            <div className={`w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold ${app.icon_url ? 'hidden' : ''}`}>
              {app.name.charAt(0).toUpperCase()}
            </div>
          </div>
          
          {/* 应用信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <Link
                href={`/apps/${app.id}`}
                className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors truncate"
              >
                {app.name}
              </Link>
              
              {/* VIP标识 */}
              {app.users?.is_vip && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  <StarIconSolid className="w-3 h-3 mr-1" />
                  VIP
                </span>
              )}
              
              {/* 认证标识 */}
              {app.users?.is_verified && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  已认证
                </span>
              )}
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
              <span className="flex items-center">
                <span className="mr-1">{categoryInfo.icon}</span>
                {categoryInfo.name}
              </span>
              
              <span className="flex items-center">
                <UserIcon className="w-4 h-4 mr-1" />
                {app.developer}
              </span>
              
              <span className="flex items-center">
                <ClockIcon className="w-4 h-4 mr-1" />
                {formatTimeAgo(app.created_at)}
              </span>
            </div>
            
            <p className="text-gray-600 text-sm line-clamp-2 mb-3">
              {app.description}
            </p>
            
            {/* 统计信息 */}
            {showStats && (
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center text-orange-600">
                  <FireIcon className="w-4 h-4 mr-1" />
                  <span className="font-medium">{app.hot_score}</span>
                  <span className="text-gray-500 ml-1">热度</span>
                </div>
                
                <div className="flex items-center text-blue-600">
                  <StarIcon className="w-4 h-4 mr-1" />
                  <span className="font-medium">{app.activity_count}</span>
                  <span className="text-gray-500 ml-1">活动</span>
                </div>
                
                <div className="text-gray-500">
                  by {app.users?.full_name || app.users?.username || '匿名用户'}
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex space-x-2">
            <Link
              href={`/apps/${app.id}`}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              查看详情
            </Link>
            
            {app.website_url && (
              <a
                href={app.website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                官网
              </a>
            )}
            
            {app.download_url && (
              <a
                href={app.download_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                下载
              </a>
            )}
          </div>
          
          {/* 热度指示器 */}
          <div className="flex items-center">
            {app.hot_score > 10 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                🔥 热门
              </span>
            )}
            {app.activity_count > 5 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 ml-2">
                ⚡ 活跃
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
