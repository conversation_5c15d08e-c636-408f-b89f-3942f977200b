import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database'

type Tables = Database['public']['Tables']
type App = Tables['apps']['Row']
type Activity = Tables['activities']['Row']
type User = Tables['users']['Row']

// 应用相关查询
export async function getApps(options?: {
  category?: string
  limit?: number
  sortBy?: 'hot_score' | 'created_at' | 'activity_count'
}) {
  const supabase = await createClient()
  
  let query = supabase
    .from('apps')
    .select(`
      *,
      users:created_by (
        username,
        full_name
      )
    `)

  if (options?.category) {
    query = query.eq('category', options.category)
  }

  if (options?.sortBy) {
    query = query.order(options.sortBy, { ascending: false })
  } else {
    query = query.order('hot_score', { ascending: false })
  }

  if (options?.limit) {
    query = query.limit(options.limit)
  }

  const { data, error } = await query

  if (error) throw error
  return data
}

export async function getAppById(id: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('apps')
    .select(`
      *,
      users:created_by (
        username,
        full_name,
        is_verified
      ),
      activities (
        id,
        title,
        description,
        total_codes,
        used_codes,
        is_active,
        expires_at,
        created_at
      )
    `)
    .eq('id', id)
    .single()

  if (error) throw error
  return data
}

// 活动相关查询
export async function getActivities(options?: {
  appId?: string
  isActive?: boolean
  limit?: number
}) {
  const supabase = await createClient()
  
  let query = supabase
    .from('activities')
    .select(`
      *,
      apps (
        name,
        icon_url,
        category
      ),
      users:created_by (
        username,
        full_name,
        is_vip
      )
    `)

  if (options?.appId) {
    query = query.eq('app_id', options.appId)
  }

  if (options?.isActive !== undefined) {
    query = query.eq('is_active', options.isActive)
  }

  query = query.order('created_at', { ascending: false })

  if (options?.limit) {
    query = query.limit(options.limit)
  }

  const { data, error } = await query

  if (error) throw error
  return data
}

export async function getActivityById(id: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('activities')
    .select(`
      *,
      apps (
        name,
        icon_url,
        category,
        developer
      ),
      users:created_by (
        username,
        full_name,
        is_vip,
        is_verified
      )
    `)
    .eq('id', id)
    .single()

  if (error) throw error
  return data
}

// 用户相关查询
export async function getUserProfile(userId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) throw error
  return data
}

export async function getUserApps(userId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('apps')
    .select(`
      *,
      activities (
        id,
        title,
        is_active,
        total_codes,
        used_codes
      )
    `)
    .eq('created_by', userId)
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export async function getUserActivities(userId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('activities')
    .select(`
      *,
      apps (
        name,
        icon_url
      )
    `)
    .eq('created_by', userId)
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}

// 订阅相关查询
export async function getUserSubscriptions(userId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('user_subscriptions')
    .select(`
      *,
      apps (
        id,
        name,
        icon_url,
        category,
        hot_score
      )
    `)
    .eq('user_id', userId)
    .order('subscribed_at', { ascending: false })

  if (error) throw error
  return data
}

export async function isUserSubscribed(userId: string, appId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('user_subscriptions')
    .select('id')
    .eq('user_id', userId)
    .eq('app_id', appId)
    .single()

  return { isSubscribed: !!data, error }
}

// 邀请码相关查询
export async function getUserClaims(userId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('code_claims')
    .select(`
      *,
      activities (
        title,
        apps (
          name,
          icon_url
        )
      )
    `)
    .eq('user_id', userId)
    .order('claimed_at', { ascending: false })

  if (error) throw error
  return data
}

export async function hasUserClaimedActivity(userId: string, activityId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('code_claims')
    .select('id')
    .eq('user_id', userId)
    .eq('activity_id', activityId)
    .single()

  return { hasClaimed: !!data, error }
}

// 统计查询
export async function getAppCategories() {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('apps')
    .select('category')
    .order('category')

  if (error) throw error
  
  // 去重并统计
  const categories = data.reduce((acc, app) => {
    acc[app.category] = (acc[app.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return Object.entries(categories).map(([name, count]) => ({
    name,
    count
  }))
}

export async function getHotApps(limit = 10) {
  return getApps({ limit, sortBy: 'hot_score' })
}

export async function getRecentActivities(limit = 20) {
  return getActivities({ isActive: true, limit })
}
