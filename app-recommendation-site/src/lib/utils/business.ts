import { Database } from '@/types/database'

type Tables = Database['public']['Tables']
type Activity = Tables['activities']['Row']
type User = Tables['users']['Row']
type App = Tables['apps']['Row']

// 活动状态检查
export function isActivityActive(activity: Activity): boolean {
  if (!activity.is_active) return false
  
  if (activity.expires_at) {
    const expiryDate = new Date(activity.expires_at)
    const now = new Date()
    if (expiryDate <= now) return false
  }
  
  return activity.used_codes < activity.total_codes
}

export function getActivityStatus(activity: Activity): {
  status: 'active' | 'expired' | 'full' | 'inactive'
  message: string
} {
  if (!activity.is_active) {
    return { status: 'inactive', message: '活动已停止' }
  }
  
  if (activity.expires_at) {
    const expiryDate = new Date(activity.expires_at)
    const now = new Date()
    if (expiryDate <= now) {
      return { status: 'expired', message: '活动已过期' }
    }
  }
  
  if (activity.used_codes >= activity.total_codes) {
    return { status: 'full', message: '邀请码已全部领取完毕' }
  }
  
  return { status: 'active', message: '活动进行中' }
}

// 用户权限检查
export function canUserClaimActivity(
  user: User,
  activity: Activity,
  userSubscriptions: string[] = []
): {
  canClaim: boolean
  reasons: string[]
} {
  const reasons: string[] = []
  
  // 检查实名认证要求
  if (activity.requires_verification && !user.is_verified) {
    reasons.push('需要完成实名认证')
  }
  
  // 检查应用订阅要求
  if (activity.requires_subscription && activity.required_apps) {
    const missingApps = activity.required_apps.filter(
      appId => !userSubscriptions.includes(appId)
    )
    if (missingApps.length > 0) {
      reasons.push(`需要订阅指定的应用 (${missingApps.length}个)`)
    }
  }
  
  // 检查活动状态
  const activityStatus = getActivityStatus(activity)
  if (activityStatus.status !== 'active') {
    reasons.push(activityStatus.message)
  }
  
  return {
    canClaim: reasons.length === 0,
    reasons
  }
}

export function isVipUser(user: User): boolean {
  if (!user.is_vip) return false
  
  if (user.vip_expires_at) {
    const expiryDate = new Date(user.vip_expires_at)
    const now = new Date()
    return expiryDate > now
  }
  
  return true
}

// 应用排名计算
export function calculateHotScore(
  totalActivities: number,
  recentActivities: number,
  totalClaims: number = 0,
  recentClaims: number = 0
): number {
  // 基础分数：总活动数
  let score = totalActivities
  
  // 最近活动加权（最近30天的活动权重更高）
  score += recentActivities * 2
  
  // 邀请码领取数加权
  score += totalClaims * 0.1
  score += recentClaims * 0.2
  
  return Math.round(score * 100) / 100
}

// 活动推荐算法
export function rankActivities(activities: Activity[]): Activity[] {
  return activities.sort((a, b) => {
    // 1. 优先显示活跃的活动
    const aActive = isActivityActive(a) ? 1 : 0
    const bActive = isActivityActive(b) ? 1 : 0
    if (aActive !== bActive) return bActive - aActive
    
    // 2. 按剩余邀请码比例排序
    const aRemaining = (a.total_codes - a.used_codes) / a.total_codes
    const bRemaining = (b.total_codes - b.used_codes) / b.total_codes
    if (Math.abs(aRemaining - bRemaining) > 0.1) {
      return bRemaining - aRemaining
    }
    
    // 3. 按创建时间排序（新的优先）
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  })
}

// 应用分类管理
export const APP_CATEGORIES = [
  { id: 'productivity', name: '效率工具', icon: '⚡' },
  { id: 'social', name: '社交网络', icon: '👥' },
  { id: 'entertainment', name: '娱乐休闲', icon: '🎮' },
  { id: 'education', name: '教育学习', icon: '📚' },
  { id: 'finance', name: '金融理财', icon: '💰' },
  { id: 'health', name: '健康医疗', icon: '🏥' },
  { id: 'shopping', name: '购物消费', icon: '🛒' },
  { id: 'travel', name: '旅行出行', icon: '✈️' },
  { id: 'news', name: '新闻资讯', icon: '📰' },
  { id: 'photography', name: '摄影图像', icon: '📸' },
  { id: 'music', name: '音乐音频', icon: '🎵' },
  { id: 'video', name: '视频播放', icon: '🎬' },
  { id: 'tools', name: '实用工具', icon: '🔧' },
  { id: 'other', name: '其他', icon: '📱' }
] as const

export function getCategoryInfo(categoryId: string) {
  return APP_CATEGORIES.find(cat => cat.id === categoryId) || APP_CATEGORIES[APP_CATEGORIES.length - 1]
}

// 时间格式化
export function formatTimeAgo(date: string | Date): string {
  const now = new Date()
  const target = new Date(date)
  const diffMs = now.getTime() - target.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
  return `${Math.floor(diffDays / 365)}年前`
}

export function formatExpiryTime(expiryDate: string | Date): string {
  const now = new Date()
  const target = new Date(expiryDate)
  const diffMs = target.getTime() - now.getTime()
  
  if (diffMs <= 0) return '已过期'
  
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 60) return `${diffMinutes}分钟后过期`
  if (diffHours < 24) return `${diffHours}小时后过期`
  return `${diffDays}天后过期`
}

// 邀请码生成
export function generateInviteCodes(count: number, prefix: string = ''): string[] {
  const codes: string[] = []
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  
  for (let i = 0; i < count; i++) {
    let code = prefix
    for (let j = 0; j < 8; j++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    codes.push(code)
  }
  
  return codes
}

// 统计数据计算
export function calculateActivityStats(activities: Activity[]) {
  const total = activities.length
  const active = activities.filter(isActivityActive).length
  const totalCodes = activities.reduce((sum, act) => sum + act.total_codes, 0)
  const usedCodes = activities.reduce((sum, act) => sum + act.used_codes, 0)
  const claimRate = totalCodes > 0 ? (usedCodes / totalCodes) * 100 : 0
  
  return {
    total,
    active,
    inactive: total - active,
    totalCodes,
    usedCodes,
    remainingCodes: totalCodes - usedCodes,
    claimRate: Math.round(claimRate * 100) / 100
  }
}

// VIP功能检查
export function getVipFeatures(isVip: boolean) {
  const baseFeatures = [
    '创建基础送码活动',
    '查看活动统计',
    '管理自己的应用'
  ]
  
  const vipFeatures = [
    '设置实名认证要求',
    '设置应用订阅要求',
    '添加反馈问卷',
    '高级活动统计',
    '批量创建活动',
    '优先展示标识'
  ]
  
  return {
    available: isVip ? [...baseFeatures, ...vipFeatures] : baseFeatures,
    locked: isVip ? [] : vipFeatures
  }
}
