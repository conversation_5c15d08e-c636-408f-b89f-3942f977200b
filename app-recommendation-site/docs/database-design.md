# 数据库设计文档

## 概述
应用推荐网站的数据库设计，基于"送码活动"的核心业务逻辑。

## 核心实体关系

### 1. 用户系统 (users)
- **主键**: id (UUID, 关联auth.users)
- **基本信息**: email, username, full_name, avatar_url
- **认证状态**: is_verified (实名认证)
- **会员状态**: is_vip, vip_expires_at
- **时间戳**: created_at, updated_at

### 2. 应用管理 (apps)
- **主键**: id (UUID)
- **基本信息**: name, description, icon_url, category, developer
- **链接**: website_url, download_url
- **创建者**: created_by (关联users.id)
- **统计数据**: activity_count (活动数量), hot_score (热度分数)
- **时间戳**: created_at, updated_at

### 3. 送码活动 (activities)
- **主键**: id (UUID)
- **关联应用**: app_id (关联apps.id)
- **活动信息**: title, description
- **邀请码**: invite_codes (数组), total_codes, used_codes
- **创建者**: created_by (关联users.id)
- **VIP条件**: 
  - requires_verification (需要实名认证)
  - requires_subscription (需要订阅应用)
  - required_apps (需要订阅的应用列表)
- **反馈设置**: feedback_required, feedback_questions
- **状态**: is_active, expires_at
- **时间戳**: created_at, updated_at

### 4. 邀请码领取 (code_claims)
- **主键**: id (UUID)
- **关联**: activity_id, user_id
- **邀请码**: invite_code
- **反馈**: feedback_submitted, feedback_data (JSONB)
- **时间戳**: claimed_at

### 5. 用户订阅 (user_subscriptions)
- **主键**: id (UUID)
- **关联**: user_id, app_id
- **时间戳**: subscribed_at

## 业务逻辑

### 排名算法
```sql
hot_score = activity_count + (recent_activity_count * 2)
```
- 总活动数 + 最近30天活动数×2

### VIP功能限制
1. **普通用户**: 只能创建基础送码活动
2. **VIP用户**: 可设置高级条件
   - 实名认证要求
   - 应用订阅要求
   - 反馈问卷要求

### 权限控制 (RLS)
- 用户只能修改自己的数据
- 应用和活动的创建者拥有管理权限
- 公开数据（应用列表、活动列表）所有人可查看

## 索引策略
- 应用分类索引
- 热度分数排序索引
- 活动状态索引
- 用户关联索引

## 触发器功能
1. **自动用户创建**: 注册时自动创建用户记录
2. **热度更新**: 创建活动时自动更新应用热度
3. **时间戳更新**: 记录更新时自动更新updated_at
