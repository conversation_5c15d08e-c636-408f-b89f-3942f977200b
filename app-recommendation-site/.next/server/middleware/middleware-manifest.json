{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_cbad7ffd._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_d8b63d80.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "8MEn+OHjGcgnJmZTd7lT19c78nMNYicaecP8NwPKq80=", "__NEXT_PREVIEW_MODE_ID": "340583256ad4926b96718821831a0c19", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fdb79ac3e6af86a2fe1cca2b66542ca949937d5770cabc13867e8f808d828d9a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "181dfb15c76555423baab8caf6fc55768e8a6b402584583ae77f6bc2e0284bb8"}}}, "instrumentation": null, "functions": {}}